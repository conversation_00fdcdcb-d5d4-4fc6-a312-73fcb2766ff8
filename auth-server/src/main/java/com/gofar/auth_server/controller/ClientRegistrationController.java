package com.gofar.auth_server.controller;


import com.gofar.auth_server.dto.ClientRegistrationDto;
import com.gofar.auth_server.service.ClientRegistrationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("clients")
public class ClientRegistrationController {

    private ClientRegistrationService service;

    @PostMapping
    @PreAuthorize("hasAuthority('SCOPE_admin')")
    public ResponseEntity<Void> register(@RequestBody ClientRegistrationDto dto) {
        service.register(dto);
        return ResponseEntity.status(HttpStatus.CREATED).build();
    }

    @GetMapping
    public ResponseEntity<List<RegisteredClient>> getAll() {
        return ResponseEntity.status(HttpStatus.OK).body(service.getAll());
    }


    @Autowired
    public void setService(ClientRegistrationService service) {
        this.service = service;
    }

}
