package com.gofar.auth_server.config;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "clients.properties")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ClientProperties {

    private Gateway gateway;
    private Frontend frontend;


    @Data
    public static class Gateway {
        private String clientId;
        private String clientSecret;
    }


    @Data
    public static class Frontend {
        private String clientId;
        private String redirectUri;
    }
}
