package com.gofar.auth_server.config;

import com.gofar.auth_server.repository.CustomRegisteredClientRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.core.AuthorizationGrantType;
import org.springframework.security.oauth2.core.ClientAuthenticationMethod;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClient;
import org.springframework.security.oauth2.server.authorization.settings.ClientSettings;
import org.springframework.security.oauth2.server.authorization.settings.TokenSettings;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

@Component
public class DataInit implements ApplicationListener<ApplicationReadyEvent> {

    private CustomRegisteredClientRepository clientRepository;
    private PasswordEncoder passwordEncoder;
    private ClientProperties clientProperties;

    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        saveGatewayClient();
        saveFrontendClient();
    }

    private void saveGatewayClient() {
        if (Objects.isNull(clientRepository.findByClientId(clientProperties.getGateway().getClientId()))) {
            TokenSettings tokenSettings = TokenSettings.builder()
                    .accessTokenTimeToLive(Duration.ofHours(5))
                    .refreshTokenTimeToLive(Duration.ofDays(1))
                    .reuseRefreshTokens(true)
                    .build();

            ClientSettings clientSettings = ClientSettings.builder()
                    .requireAuthorizationConsent(false)
                    .build();

            RegisteredClient client = RegisteredClient.withId(UUID.randomUUID().toString())
                    .clientId(clientProperties.getGateway().getClientId())
                    .clientSecret(passwordEncoder.encode(clientProperties.getGateway().getClientSecret()))
                    .clientAuthenticationMethods(authMethods ->
                            authMethods.addAll(List.of(ClientAuthenticationMethod.CLIENT_SECRET_BASIC,
                                    ClientAuthenticationMethod.CLIENT_SECRET_POST)))
                    .authorizationGrantType(AuthorizationGrantType.CLIENT_CREDENTIALS)
                    .scope("admin")
                    .scope("user")
                    .tokenSettings(tokenSettings)
                    .clientSettings(clientSettings)
                    .build();
            clientRepository.save(client);
        }
    }

    private void saveFrontendClient() {
        if (Objects.isNull(clientRepository.findByClientId(clientProperties.getFrontend().getClientId()))) {
            RegisteredClient client = RegisteredClient.withId(UUID.randomUUID().toString())
                    .clientId(clientProperties.getFrontend().getClientId())
                    .clientAuthenticationMethod(ClientAuthenticationMethod.NONE) // PKCE
                    .authorizationGrantType(AuthorizationGrantType.AUTHORIZATION_CODE)
                    .redirectUri(clientProperties.getFrontend().getRedirectUri()) // à adapter
                    .scope("openid")
                    .scope("user")
                    .clientSettings(ClientSettings.builder()
                            .requireAuthorizationConsent(false)
                            .build())
                    .tokenSettings(TokenSettings.builder()
                            .accessTokenTimeToLive(Duration.ofMinutes(30))
                            .build())
                    .build();
            clientRepository.save(client);
        }
    }


    @Autowired
    public void setClientRepository(CustomRegisteredClientRepository clientRepository) {
        this.clientRepository = clientRepository;
    }

    @Autowired
    public void setPasswordEncoder(PasswordEncoder passwordEncoder) {
        this.passwordEncoder = passwordEncoder;
    }

    @Autowired
    public void setClientProperties(ClientProperties clientProperties) {
        this.clientProperties = clientProperties;
    }
}
