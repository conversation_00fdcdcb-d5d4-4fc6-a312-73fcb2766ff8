package com.gofar.auth_server.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestClient;

/**
 * Configuration for RestClient to communicate with external services
 */
@Configuration
public class RestClientConfig {

    @Value("${user-service.base-url:http://localhost:8081}")
    private String userServiceBaseUrl;

    /**
     * RestClient bean for calling the user service
     */
    @Bean
    public RestClient userServiceRestClient() {
        return RestClient.builder()
                .baseUrl(userServiceBaseUrl)
                .build();
    }
}
