package com.gofar.auth_server.service;

import com.gofar.auth_server.dto.ValidEcoUserDetails;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.stream.Collectors;

/**
 * Custom UserDetailsService that validates users against external user service
 */
@Service
public class CustomUserDetailsService implements UserDetailsService {

    private static final Logger logger = LoggerFactory.getLogger(CustomUserDetailsService.class);
    
    private final UserServiceClient userServiceClient;

    public CustomUserDetailsService(UserServiceClient userServiceClient) {
        this.userServiceClient = userServiceClient;
    }

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        logger.debug("Loading user by username: {}", username);

        return null;
    }

    /**
     * Validates user credentials and returns UserDetails with proper authorities
     */
    public UserDetails validateAndLoadUser(String email, String password) {
        logger.debug("Validating and loading user: {}", email);
        
        ValidEcoUserDetails validUser = userServiceClient.validateUser(email, password);
        
        if (validUser == null) {
            logger.warn("User validation failed for email: {}", email);
            throw new UsernameNotFoundException("Invalid credentials for user: " + email);
        }

        // Convert scopes to Spring Security authorities
        Collection<GrantedAuthority> authorities = validUser.scopes().stream()
                .map(scope -> new SimpleGrantedAuthority("ROLE_" + scope.toUpperCase()))
                .collect(Collectors.toList());

        logger.debug("User validation successful for email: {} with authorities: {}", 
                email, authorities);

        return User.builder()
                .username(validUser.email())
                .password("") // Password already validated
                .authorities(authorities)
                .build();
    }
}
