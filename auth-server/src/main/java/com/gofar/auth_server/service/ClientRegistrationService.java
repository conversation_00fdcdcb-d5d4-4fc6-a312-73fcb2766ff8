package com.gofar.auth_server.service;

import com.gofar.auth_server.dto.ClientRegistrationDto;
import com.gofar.auth_server.repository.CustomRegisteredClientRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.core.AuthorizationGrantType;
import org.springframework.security.oauth2.core.ClientAuthenticationMethod;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClient;
import org.springframework.security.oauth2.server.authorization.settings.ClientSettings;
import org.springframework.security.oauth2.server.authorization.settings.TokenSettings;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.List;
import java.util.UUID;

@Service
public class ClientRegistrationService {

    private CustomRegisteredClientRepository customRepository;

    private PasswordEncoder encoder;

    public void register(ClientRegistrationDto dto) {
        RegisteredClient client = RegisteredClient.withId(UUID.randomUUID().toString())
                .clientId(dto.getClientId())
                .clientSecret(encoder.encode(dto.getClientSecret()))
                .clientAuthenticationMethods(methods -> methods.addAll(List.of(ClientAuthenticationMethod.CLIENT_SECRET_BASIC,
                        ClientAuthenticationMethod.CLIENT_SECRET_POST)))
                .authorizationGrantTypes(grants -> dto.getGrantTypes().forEach(gt ->
                        grants.add(new AuthorizationGrantType(gt))))
                .scopes(scopes -> scopes.addAll(dto.getScopes()))
                .tokenSettings(TokenSettings.builder()
                        .accessTokenTimeToLive(Duration.ofHours(1))
                        .build())
                .clientSettings(ClientSettings.builder().requireAuthorizationConsent(false).build())
                .build();

        customRepository.save(client);
    }

    public List<RegisteredClient> getAll() {
        return customRepository.findAll();
    }

    @Autowired
    public void setEncoder(PasswordEncoder encoder) {
        this.encoder = encoder;
    }


    @Autowired
    public void setCustomRepository(CustomRegisteredClientRepository customRepository) {
        this.customRepository = customRepository;
    }
}
