package com.gofar.auth_server.service;

import com.gofar.auth_server.dto.ClientRegistrationDto;
import com.gofar.auth_server.repository.CustomRegisteredClientRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.core.AuthorizationGrantType;
import org.springframework.security.oauth2.core.ClientAuthenticationMethod;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClient;
import org.springframework.security.oauth2.server.authorization.settings.ClientSettings;
import org.springframework.security.oauth2.server.authorization.settings.TokenSettings;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.List;
import java.util.UUID;

@Service
public class ClientRegistrationService {

    private CustomRegisteredClientRepository customRepository;

    private PasswordEncoder encoder;

    public void register(ClientRegistrationDto dto) {
        RegisteredClient.Builder clientBuilder = RegisteredClient.withId(UUID.randomUUID().toString())
                .clientId(dto.getClientId());

        // Handle client secret (not required for PKCE clients)
        if (dto.getClientSecret() != null && !dto.getClientSecret().isEmpty()) {
            clientBuilder.clientSecret(encoder.encode(dto.getClientSecret()));
        }

        // Configure authentication methods
        if (dto.getAuthenticationMethods() != null && !dto.getAuthenticationMethods().isEmpty()) {
            clientBuilder.clientAuthenticationMethods(methods -> {
                dto.getAuthenticationMethods().forEach(method -> {
                    switch (method.toUpperCase()) {
                        case "CLIENT_SECRET_BASIC" -> methods.add(ClientAuthenticationMethod.CLIENT_SECRET_BASIC);
                        case "CLIENT_SECRET_POST" -> methods.add(ClientAuthenticationMethod.CLIENT_SECRET_POST);
                        case "NONE" -> methods.add(ClientAuthenticationMethod.NONE);
                    }
                });
            });
        } else {
            // Default authentication methods for confidential clients
            clientBuilder.clientAuthenticationMethods(methods -> methods.addAll(List.of(
                    ClientAuthenticationMethod.CLIENT_SECRET_BASIC,
                    ClientAuthenticationMethod.CLIENT_SECRET_POST)));
        }

        // Configure grant types
        clientBuilder.authorizationGrantTypes(grants -> dto.getGrantTypes().forEach(gt ->
                grants.add(new AuthorizationGrantType(gt))));

        // Configure scopes
        clientBuilder.scopes(scopes -> scopes.addAll(dto.getScopes()));

        // Configure redirect URIs if provided
        if (dto.getRedirectUris() != null && !dto.getRedirectUris().isEmpty()) {
            dto.getRedirectUris().forEach(clientBuilder::redirectUri);
        }

        // Configure token settings
        clientBuilder.tokenSettings(TokenSettings.builder()
                .accessTokenTimeToLive(Duration.ofHours(1))
                .refreshTokenTimeToLive(Duration.ofHours(8))
                .build());

        // Configure client settings
        clientBuilder.clientSettings(ClientSettings.builder()
                .requireAuthorizationConsent(dto.isRequireAuthorizationConsent())
                .requireProofKey(dto.isRequireProofKey())
                .build());

        RegisteredClient client = clientBuilder.build();
        customRepository.save(client);
    }

    public List<RegisteredClient> getAll() {
        return customRepository.findAll();
    }

    @Autowired
    public void setEncoder(PasswordEncoder encoder) {
        this.encoder = encoder;
    }


    @Autowired
    public void setCustomRepository(CustomRegisteredClientRepository customRepository) {
        this.customRepository = customRepository;
    }
}
