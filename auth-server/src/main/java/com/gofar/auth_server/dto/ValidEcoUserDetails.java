package com.gofar.auth_server.dto;

import com.gofar.auth_server.enums.Role;

import java.util.List;

/**
 * DTO representing validated user details returned from user service
 *
 * @param email the email of the validated user
 * @param role the role of the user
 * @param scopes the scopes/authorities granted to the user
 */
public record ValidEcoUserDetails(
        String email,
        Role role,
        List<String> scopes
) {
}
