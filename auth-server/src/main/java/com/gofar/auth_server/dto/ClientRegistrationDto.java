package com.gofar.auth_server.dto;

import lombok.Data;

import java.util.List;


@Data
public class ClientRegistrationDto {
    private String clientId;
    private String clientSecret;
    private List<String> scopes;
    private List<String> grantTypes;
    private List<String> redirectUris;
    private boolean requireProofKey = false; // PKCE support
    private boolean requireAuthorizationConsent = false;
    private List<String> authenticationMethods; // CLIENT_SECRET_BASIC, CLIENT_SECRET_POST, NONE
}
