package com.gofar.auth_server.repository;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.security.oauth2.server.authorization.client.JdbcRegisteredClientRepository;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClient;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class CustomRegisteredClientRepository extends JdbcRegisteredClientRepository {

    private final JdbcTemplate jdbcTemplate;

    @Autowired
    public CustomRegisteredClientRepository(JdbcTemplate jdbcTemplate) {
        super(jdbcTemplate);
        this.jdbcTemplate = jdbcTemplate;
    }

    public List<RegisteredClient> findAll() {
        return jdbcTemplate.query("SELECT * FROM oauth2_registered_client",
                getRegisteredClientRowMapper());
    }
}
