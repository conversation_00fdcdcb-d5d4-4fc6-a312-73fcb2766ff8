spring:
  application.name: auth-server
  datasource:
    hostname: localhost
    port: 5432
    database: authz
    username: tountoun
    password: 12345
    url: jdbc:postgresql://${spring.datasource.hostname}:${spring.datasource.port}/${spring.datasource.database}
    driver-class-name: org.postgresql.Driver
  sql:
    init:
      mode: always
      schema-locations: classpath:/schema-oauth2.sql
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    database-platform: org.hibernate.dialect.PostgreSQLDialect

eureka:
  client:
    serviceUrl:
      defaultZone: http://localhost:8761/eureka/

clients:
  properties:
    gateway:
      client-id: eco-gateway
      client-secret: eco-secret
    frontend:
      client-id: eco-frontend
      redirect-uri: http://localhost:4200/callback

server:
  port: 9000

user-service:
  base-url: http://localhost:8081