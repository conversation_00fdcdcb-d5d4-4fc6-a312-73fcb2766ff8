package com.gofar.auth_server.service;

import com.gofar.auth_server.dto.ValidEcoUserDetails;
import com.gofar.auth_server.enums.Role;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.TestPropertySource;
import org.springframework.web.client.RestClient;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for UserServiceClient
 */
@SpringBootTest
@TestPropertySource(properties = {
    "user-service.base-url=http://localhost:8081"
})
class UserServiceClientTest {

    @MockBean
    private RestClient userServiceRestClient;

    @Test
    void testValidEcoUserDetailsCreation() {
        // Test that our DTOs work correctly
        ValidEcoUserDetails userDetails = new ValidEcoUserDetails(
            "<EMAIL>", 
            Role.USER, 
            List.of("user")
        );
        
        assertEquals("<EMAIL>", userDetails.email());
        assertEquals(Role.USER, userDetails.role());
        assertEquals(List.of("user"), userDetails.scopes());
    }
}
