spring:
  application:
    name: config-server
  cloud:
    config:
      server:
        git:
          uri: "https://github.com/Tountoun/eco-config"
          default-label: main
          clone-on-start: true
          force-pull: true
          timeout: 10
          username: ${GITHUB_USERNAME}
          password: ${GITHUB_TOKEN}

  security:
    user:
      name: ${SECURITY_USER_NAME:admin}
      password: ${SECURITY_USER_PASSWORD:123456}

management:
    endpoints:
      web:
        exposure:
          include: "*"
    endpoint:
      health:
        show-details: always

server:
  port: 8888