package com.gofar.user_service;

import com.gofar.user_service.dto.EcoUserCreationDto;
import com.gofar.user_service.entity.Role;
import com.gofar.user_service.service.EcoUserService;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;

@SpringBootApplication
public class UserServiceApplication {

	public static void main(String[] args) {
		SpringApplication.run(UserServiceApplication.class, args);
	}

	@Bean
	ApplicationRunner init(EcoUserService userService) {
		return args -> {
			final String adminEmail = "<EMAIL>";
			if (userService.findByEmail(adminEmail).isEmpty()) {
				userService.save(new EcoUserCreationDto(adminEmail, "admin", "Admin", "Admin", Role.ADMIN, null));
			}
		};
	}

}
