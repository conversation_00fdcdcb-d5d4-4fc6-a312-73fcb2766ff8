package com.gofar.user_service.dto;


import jakarta.validation.constraints.NotBlank;

/**
 * DTO for user credentials and used for authentication request
 *
 * @param email the email of the user
 * @param password the raw password of the user
 */
public record EcoUserCredentials(
        @NotBlank(message = "Email is mandatory")
        String email,
        @NotBlank(message = "Password is mandatory")
        String password
) {
}
