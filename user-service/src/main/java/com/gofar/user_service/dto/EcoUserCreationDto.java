package com.gofar.user_service.dto;

import com.gofar.user_service.entity.EnergyPreference;
import com.gofar.user_service.entity.Role;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@NoArgsConstructor
@AllArgsConstructor
@Data
@Schema(description = "DTO for creating a new eco user")
public class EcoUserCreationDto {

    @NotBlank(message = "Email is mandatory")
    @Schema(description = "User email address", example = "<EMAIL>")
    private String email;

    @NotBlank(message = "Password is mandatory")
    @Schema(description = "User password", example = "securePassword123")
    private String password;

    @NotBlank(message = "First name is mandatory")
    @Schema(description = "User first name", example = "John")
    private String firstName;

    @NotBlank(message = "Last name is mandatory")
    @Schema(description = "User last name", example = "Doe")
    private String lastName;
    @NotNull(message = "Role is mandatory")
    @Schema(description = "User role", example = "USER", allowableValues = {"ADMIN", "USER", "TECHNICIAN"})
    private Role role;

    @Valid
    @Schema(description = "User energy preferences for eco-friendly consumption",
            example = "{\"priorityEco\": true, \"EVCharging\": false, \"preferredTime\": \"MORNING\"}")
    private EnergyPreference preference;
}
