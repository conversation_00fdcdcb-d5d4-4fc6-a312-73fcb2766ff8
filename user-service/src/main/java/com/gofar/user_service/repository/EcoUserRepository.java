package com.gofar.user_service.repository;

import com.gofar.user_service.entity.EcoUser;
import jakarta.validation.constraints.NotBlank;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface EcoUserRepository extends JpaRepository<EcoUser, Long> {

    Optional<EcoUser> findByEmail(String email);

    boolean existsByEmail(@NotBlank(message = "Email is mandatory") String email);
}
