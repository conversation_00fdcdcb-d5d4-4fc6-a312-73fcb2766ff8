package com.gofar.user_service.service;

import com.gofar.user_service.dto.EcoUserCreationDto;
import com.gofar.user_service.dto.EcoUserResponseDto;
import com.gofar.user_service.dto.ValidEcoUserDetails;
import com.gofar.user_service.entity.EcoUser;
import com.gofar.user_service.repository.EcoUserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
public class EcoUserService {

    private final EcoUserRepository ecoUserRepository;
    private PasswordEncoder passwordEncoder;

    public EcoUserService(EcoUserRepository ecoUserRepository) {
        this.ecoUserRepository = ecoUserRepository;
    }

    public Optional<EcoUserResponseDto> findByEmail(String email) {
        return ecoUserRepository.findByEmail(email)
                .map(user -> new EcoUserResponseDto(user.getId(),
                        user.getEmail(), user.getFirstName(), user.getLastName(), user.getRole(), user.getPreference()));
    }

    public EcoUserResponseDto save(EcoUserCreationDto ecoUserDto) {
        if (ecoUserRepository.existsByEmail(ecoUserDto.getEmail())) {
            throw new IllegalArgumentException("Email already exists");
        }
        EcoUser user = new EcoUser();
        user.setFirstName(ecoUserDto.getFirstName());
        user.setLastName(ecoUserDto.getLastName());
        user.setEmail(ecoUserDto.getEmail());
        user.setPassword(passwordEncoder.encode(ecoUserDto.getPassword()));
        user.setRole(ecoUserDto.getRole());
        user.setPreference(ecoUserDto.getPreference());
        ecoUserRepository.save(user);
        return new EcoUserResponseDto(user.getId(),
                user.getEmail(), user.getFirstName(), user.getLastName(), user.getRole(), user.getPreference());
    }

    public ValidEcoUserDetails validateUser(String email, String password) {
        Optional<EcoUser> userOptional = ecoUserRepository.findByEmail(email);
        if (userOptional.isPresent() && passwordEncoder.matches(password, userOptional.get().getPassword())) {
            EcoUser user = userOptional.get();
            List<String> scopes = switch (user.getRole()) {
                case ADMIN -> new ArrayList<>(List.of("admin", "user"));
                case USER -> new ArrayList<>(List.of("user"));
                case TECHNICIAN -> new ArrayList<>(List.of("technician"));
            };
            return new ValidEcoUserDetails(user.getEmail(), user.getRole(), scopes);
        }
        return null;
    }


    @Autowired
    public void setPasswordEncoder(PasswordEncoder passwordEncoder) {
        this.passwordEncoder = passwordEncoder;
    }
}
