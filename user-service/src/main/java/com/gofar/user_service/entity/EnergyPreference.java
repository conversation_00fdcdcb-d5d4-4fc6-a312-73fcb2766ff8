package com.gofar.user_service.entity;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Embeddable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Embeddable
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Energy consumption preferences for eco-friendly usage")
public class EnergyPreference {

    @Schema(description = "Whether to prioritize eco-friendly energy consumption", example = "true")
    private boolean priorityEco;

    @Schema(description = "Whether electric vehicle charging is enabled", example = "false")
    private boolean EVCharging;

    @Schema(description = "Preferred time slot for energy consumption", example = "MORNING")
    private PreferredTime preferredTime;

    @Getter
    public enum PreferredTime {
        EARLY_MORNING("06:00-08:00"),
        MORNING("08:00-12:00"),
        AFTERNOON("12:00-16:00"),
        EVENING("16:00-20:00"),
        NIGHT("20:00-23:00");

        private final String timeRange;

        PreferredTime(String timeRange) {
            this.timeRange = timeRange;
        }

    }
}
