package com.gofar.user_service.entity;


import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

@Entity
@Table(name = "ECUSR")
@Data
@NoArgsConstructor
public class EcoUser {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private UUID id;
    @Column(name = "USRML")
    private String email;
    @Column(name = "USRPWD")
    private String password;
    @Enumerated(EnumType.STRING)
    private Role role;
    @Column(name = "USRFNM")
    private String firstName;
    @Column(name = "USRLNM")
    private String lastName;
    @Embedded
    private EnergyPreference preference;
}
