spring:
  application:
    name: user-service
  config:
    import: "configserver:http://localhost:8888"
  cloud:
    config:
      username: ${CONFIG_SERVER_USER_NAME:admin}
      password: ${CONFIG_SERVER_PASSWORD:123456}
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: "http://localhost:9000"
          jwk-set-uri: "http://localhost:9000/oauth2/v1/jwks"
  datasource:
    hostname: localhost
    port: 5434
    database: eco_users
    username: tountoun
    password: 12345
    url: jdbc:postgresql://${spring.datasource.hostname}:${spring.datasource.port}/${spring.datasource.database}
    driver-class-name: org.postgresql.Driver
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    database-platform: org.hibernate.dialect.PostgreSQLDialect

eureka:
  client:
    serviceUrl:
      defaultZone: http://localhost:8761/eureka/

server:
  port: 8081